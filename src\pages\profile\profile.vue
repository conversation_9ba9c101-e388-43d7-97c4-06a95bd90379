<template>
  <view class="profile-container">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="avatar-section">
        <image 
          class="avatar" 
          :src="userInfo.avatar || '/static/default-avatar.png'"
          mode="aspectFill"
        />
        <view class="user-info">
          <text class="username">{{ userInfo.nickname || '未登录用户' }}</text>
          <text class="user-id">ID: {{ userInfo.deviceId || 'N/A' }}</text>
        </view>
      </view>
      <view class="login-btn" @click="handleLogin" v-if="!userInfo.isLogin">
        <text>登录</text>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stat-item">
        <text class="stat-number">{{ stats.totalTasks }}</text>
        <text class="stat-label">处理任务</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.completedTasks }}</text>
        <text class="stat-label">已完成</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.totalDuration }}</text>
        <text class="stat-label">总时长(分钟)</text>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item" @click="navigateTo('/pages/history/history')">
        <view class="menu-icon">📋</view>
        <text class="menu-text">历史记录</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="navigateTo('/pages/settings/settings')">
        <view class="menu-icon">⚙️</view>
        <text class="menu-text">设置</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="showAbout">
        <view class="menu-icon">ℹ️</view>
        <text class="menu-text">关于我们</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="showHelp">
        <view class="menu-icon">❓</view>
        <text class="menu-text">帮助与反馈</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 版本信息 -->
    <view class="version-info">
      <text class="version-text">智能字幕胶囊 v1.0.0</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getDeviceId } from '@/utils/common'
import { getHistoryList } from '@/utils/api'

// 用户信息
const userInfo = ref({
  nickname: '',
  avatar: '',
  deviceId: '',
  isLogin: false
})

// 统计信息
const stats = ref({
  totalTasks: 0,
  completedTasks: 0,
  totalDuration: 0
})

// 页面加载时获取用户信息和统计数据
onMounted(async () => {
  await loadUserInfo()
  await loadStats()
})

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const deviceId = getDeviceId()
    userInfo.value.deviceId = deviceId
    userInfo.value.nickname = `用户${deviceId.slice(-6)}`
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 加载统计数据
const loadStats = async () => {
  try {
    const historyList = await getHistoryList(0, 100)
    stats.value.totalTasks = historyList.length
    stats.value.completedTasks = historyList.filter(task => task.status === 'completed').length
    
    // 计算总时长（示例数据）
    stats.value.totalDuration = historyList.reduce((total, task) => {
      return total + (task.duration || 0)
    }, 0)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 处理登录
const handleLogin = () => {
  uni.showToast({
    title: '登录功能开发中',
    icon: 'none'
  })
}

// 页面导航
const navigateTo = (url: string) => {
  uni.navigateTo({ url })
}

// 显示关于信息
const showAbout = () => {
  uni.showModal({
    title: '关于我们',
    content: '智能字幕胶囊是一款AI驱动的视频字幕生成工具，支持视频添加字幕、字幕翻译、视频去水印等功能。',
    showCancel: false
  })
}

// 显示帮助信息
const showHelp = () => {
  uni.showModal({
    title: '帮助与反馈',
    content: '如有问题或建议，请联系我们的客服团队。我们将竭诚为您服务！',
    showCancel: false
  })
}
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.user-header {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  padding: 40rpx 30rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.avatar-section {
  display: flex;
  align-items: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 30rpx;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.user-id {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-btn {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.login-btn text {
  color: white;
  font-size: 28rpx;
}

.stats-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.menu-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f8f8;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 30rpx;
  width: 60rpx;
  text-align: center;
}

.menu-text {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.menu-arrow {
  font-size: 32rpx;
  color: #ccc;
}

.version-info {
  text-align: center;
  padding: 40rpx;
}

.version-text {
  font-size: 24rpx;
  color: #999;
}
</style>
